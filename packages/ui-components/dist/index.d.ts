import React from 'react';
export * from '@monorepo/utils';

interface ButtonProps {
    children: React.ReactNode;
    variant?: 'primary' | 'secondary' | 'danger';
    size?: 'small' | 'medium' | 'large';
    disabled?: boolean;
    onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
    className?: string;
    type?: 'button' | 'submit' | 'reset';
}
declare const Button: React.FC<ButtonProps>;

interface InputProps {
    label?: string;
    placeholder?: string;
    value?: string;
    defaultValue?: string;
    type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
    disabled?: boolean;
    required?: boolean;
    error?: string;
    className?: string;
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
    onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
}
declare const Input: React.FC<InputProps>;

interface CardProps {
    children: React.ReactNode;
    title?: string;
    subtitle?: string;
    className?: string;
    onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
    hoverable?: boolean;
}
declare const Card: React.FC<CardProps>;

export { Button, Card, Input };
export type { ButtonProps, CardProps, InputProps };
