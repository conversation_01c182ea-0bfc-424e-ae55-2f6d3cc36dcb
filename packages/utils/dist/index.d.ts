declare function capitalize(str: string): string;
declare function camelCase(str: string): string;
declare function kebabCase(str: string): string;
declare function chunk<T>(array: T[], size: number): T[][];
declare function unique<T>(array: T[]): T[];
declare function groupBy<T, K extends string | number | symbol>(array: T[], keyFn: (item: T) => K): Record<K, T[]>;
declare function pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K>;
declare function omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K>;
declare function isString(value: unknown): value is string;
declare function isNumber(value: unknown): value is number;
declare function isObject(value: unknown): value is Record<string, unknown>;
declare function isArray(value: unknown): value is unknown[];
declare function delay(ms: number): Promise<void>;
declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
declare function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void;

export { camelCase, capitalize, chunk, debounce, delay, groupBy, isArray, isNumber, isObject, isString, kebabCase, omit, pick, throttle, unique };
