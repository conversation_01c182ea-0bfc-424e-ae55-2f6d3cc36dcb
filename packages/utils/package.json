{"name": "@monorepo/utils", "version": "1.0.0", "type": "module", "description": "Utility functions for the monorepo", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "echo \"No tests yet\""}, "keywords": ["utils", "typescript", "monorepo"], "author": "", "license": "MIT", "devDependencies": {"typescript": "^5.3.3"}, "publishConfig": {"access": "public"}}