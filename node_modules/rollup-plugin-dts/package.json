{"name": "rollup-plugin-dts", "version": "6.2.3", "description": "A rollup plugin that will bundle up your .d.ts definition files.", "keywords": ["rollup-plugin", "typescript", "dts", "@types"], "author": "Arpad Borsos <<EMAIL>>", "license": "LGPL-3.0-only", "repository": {"type": "git", "url": "git+https://github.com/Swatinem/rollup-plugin-dts.git"}, "bugs": {"url": "https://github.com/Swatinem/rollup-plugin-dts/issues"}, "funding": {"url": "https://github.com/sponsors/Swatinem"}, "homepage": "https://github.com/Swatinem/rollup-plugin-dts#readme", "engines": {"node": ">=16"}, "type": "module", "main": "./dist/rollup-plugin-dts.cjs", "types": "./dist/rollup-plugin-dts.d.cts", "exports": {"import": "./dist/rollup-plugin-dts.mjs", "require": "./dist/rollup-plugin-dts.cjs"}, "sideEffects": false, "files": ["dist"], "scripts": {"clean": "node -e \"(async () => { try { await require('fs/promises').rm('dist', { recursive: true }); } catch {} })()\"", "prebuild": "npm run clean", "build": "tsc && rollup --config .build/rollup.config.js", "prepublishOnly": "npm run test", "pretest": "npm run build", "test": "c8 node .build/tests/index.js"}, "prettier": {"printWidth": 120, "trailingComma": "all"}, "devDependencies": {"@babel/code-frame": "^7.27.1", "@types/babel__code-frame": "^7.0.6", "@types/d3-drag": "^3.0.7", "@types/estree": "1.0.8", "@types/node": "^24.2.1", "@types/react": "^19.1.9", "c8": "^10.1.3", "rollup": "4.46.2", "typescript": "5.9.2"}, "peerDependencies": {"rollup": "^3.29.4 || ^4", "typescript": "^4.5 || ^5.0"}, "optionalDependencies": {"@babel/code-frame": "^7.27.1"}, "dependencies": {"magic-string": "^0.30.17"}}