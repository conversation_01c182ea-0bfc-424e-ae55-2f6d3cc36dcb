# Monorepo Example

A modern monorepo setup using TypeScript, Rollup, and Yarn workspaces.

## 🏗️ Structure

```
monorepo/
├── packages/
│   ├── utils/                 # Utility functions
│   └── ui-components/         # React UI components
├── apps/
│   └── example-app/           # Example application
├── rollup.config.base.js      # Shared Rollup configuration
├── tsconfig.json              # Root TypeScript configuration
└── package.json               # Root package.json with workspaces
```

## 📦 Packages

### @monorepo/utils
Common utility functions including:
- String utilities (`capitalize`, `camelCase`, `kebabCase`)
- Array utilities (`chunk`, `unique`, `groupBy`)
- Object utilities (`pick`, `omit`)
- Type guards (`isString`, `isNumber`, `isObject`, `isArray`)
- Async utilities (`delay`, `debounce`, `throttle`)

### @monorepo/ui-components
React UI components including:
- `Button` - Customizable button component
- `Input` - Form input with validation
- `Card` - Container component with optional header

### example-app
A sample React application demonstrating the usage of both packages.

## 🚀 Getting Started

### Prerequisites
- Node.js >= 18.0.0
- Yarn >= 4.0.0

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   yarn install
   ```

### Development

```bash
# Build all packages
yarn build

# Run in development mode (watch mode)
yarn dev

# Type checking
yarn type-check

# Linting
yarn lint
yarn lint:fix

# Clean all build outputs
yarn clean
```

### Working with individual packages

```bash
# Build specific package
yarn workspace @monorepo/utils build

# Run specific package in dev mode
yarn workspace @monorepo/ui-components dev

# Add dependency to specific package
yarn workspace @monorepo/utils add lodash
```

## 🛠️ Tools & Configuration

- **TypeScript**: Full type safety across all packages
- **Rollup**: Modern bundling with shared configuration
- **ESLint + Prettier**: Code quality and formatting
- **Changesets**: Version management and changelog generation
- **Yarn Workspaces**: Dependency management and linking

## 📝 Scripts

- `yarn build` - Build all packages
- `yarn dev` - Run all packages in watch mode
- `yarn test` - Run tests (placeholder)
- `yarn lint` - Lint all packages
- `yarn lint:fix` - Fix linting issues
- `yarn type-check` - Type check all packages
- `yarn clean` - Clean all build outputs
- `yarn changeset` - Create a changeset
- `yarn version-packages` - Version packages
- `yarn release` - Build and publish packages

## 🔧 Configuration Files

- `rollup.config.base.js` - Shared Rollup configuration
- `tsconfig.json` - Root TypeScript configuration
- `.eslintrc.js` - ESLint configuration
- `.prettierrc` - Prettier configuration
- `.changeset/config.json` - Changesets configuration

## 📄 License

MIT
