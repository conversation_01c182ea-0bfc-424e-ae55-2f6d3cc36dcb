{"name": "@monorepo/ui-components", "version": "1.0.0", "description": "React UI components for the monorepo", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "echo \"No tests yet\""}, "keywords": ["react", "components", "ui", "typescript", "monorepo"], "author": "", "license": "MIT", "dependencies": {"@monorepo/utils": "file:../utils"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3"}, "publishConfig": {"access": "public"}}