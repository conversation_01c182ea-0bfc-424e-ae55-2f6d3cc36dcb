import React from 'react';
import { kebabCase } from '@monorepo/utils';

export interface InputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
  disabled?: boolean;
  required?: boolean;
  error?: string;
  className?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
}

const Input: React.FC<InputProps> = ({
  label,
  placeholder,
  value,
  defaultValue,
  type = 'text',
  disabled = false,
  required = false,
  error,
  className = '',
  onChange,
  onBlur,
  onFocus,
}) => {
  const inputId = label ? kebabCase(label) : undefined;
  
  const inputClasses = [
    'input',
    error ? 'input--error' : '',
    disabled ? 'input--disabled' : '',
    className,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className="input-wrapper">
      {label && (
        <label 
          htmlFor={inputId}
          className="input-label"
          style={{
            display: 'block',
            marginBottom: '4px',
            fontSize: '14px',
            fontWeight: '500',
            color: error ? '#dc3545' : '#333',
          }}
        >
          {label}
          {required && <span style={{ color: '#dc3545' }}>*</span>}
        </label>
      )}
      <input
        id={inputId}
        type={type}
        className={inputClasses}
        placeholder={placeholder}
        value={value}
        defaultValue={defaultValue}
        disabled={disabled}
        required={required}
        onChange={onChange}
        onBlur={onBlur}
        onFocus={onFocus}
        style={{
          width: '100%',
          padding: '8px 12px',
          fontSize: '14px',
          border: `1px solid ${error ? '#dc3545' : '#ccc'}`,
          borderRadius: '4px',
          outline: 'none',
          backgroundColor: disabled ? '#f5f5f5' : 'white',
          cursor: disabled ? 'not-allowed' : 'text',
        }}
      />
      {error && (
        <div 
          className="input-error"
          style={{
            marginTop: '4px',
            fontSize: '12px',
            color: '#dc3545',
          }}
        >
          {error}
        </div>
      )}
    </div>
  );
};

export default Input;
