{"name": "monorepo-example", "version": "1.0.0", "private": true, "type": "module", "description": "A modern monorepo with TypeScript, Rollup, and Yarn workspaces", "workspaces": ["packages/*", "apps/*"], "scripts": {"build": "yarn workspace @monorepo/utils build && yarn workspace @monorepo/ui-components build && yarn workspace example-app build", "dev": "yarn workspace @monorepo/utils dev & yarn workspace @monorepo/ui-components dev & yarn workspace example-app dev", "test": "echo \"No tests configured yet\"", "type-check": "yarn workspace @monorepo/utils type-check && yarn workspace @monorepo/ui-components type-check && yarn workspace example-app type-check", "clean": "yarn workspace @monorepo/utils clean && yarn workspace @monorepo/ui-components clean && yarn workspace example-app clean"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.5", "@types/node": "^20.10.5", "rollup": "^4.9.1", "rollup-plugin-dts": "^6.1.0", "tslib": "^2.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "yarn": ">=1.22.0"}}