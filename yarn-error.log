Arguments: 
  /usr/local/bin/node /usr/local/bin/yarn install

PATH: 
  /usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/Wireshark.app/Contents/MacOS:/usr/local/share/dotnet:~/.dotnet/tools:/Users/<USER>/.nvm/versions/node/v18.13.0/bin:/Users/<USER>/.yarn/bin:/opt/homebrew/opt/ruby/bin:/opt/homebrew/opt/python@3.11/libexec/bin:/Library/Frameworks/Python.framework/Versions/3.11/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/sdk/flutter/bin:/Users/<USER>/anaconda3/condabin:/Users/<USER>/Library/pnpm:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Android/sdk/emulator:/Users/<USER>/Library/Android/sdk/tools:/Users/<USER>/Library/Android/sdk/tools/bin:/Users/<USER>/Library/Android/sdk/platform-tools:/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand

Yarn version: 
  1.22.19

Node version: 
  18.17.1

Platform: 
  darwin arm64

Trace: 
  Error: getaddrinfo ENOTFOUND krakengine.com
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:108:26)

npm manifest: 
  {
    "name": "monorepo-example",
    "version": "1.0.0",
    "private": true,
    "description": "A modern monorepo with TypeScript, Rollup, and Yarn workspaces",
    "workspaces": [
      "packages/*",
      "apps/*"
    ],
    "scripts": {
      "build": "yarn workspaces foreach -A run build",
      "dev": "yarn workspaces foreach -A run dev",
      "test": "yarn workspaces foreach -A run test",
      "lint": "yarn workspaces foreach -A run lint",
      "lint:fix": "yarn workspaces foreach -A run lint:fix",
      "type-check": "yarn workspaces foreach -A run type-check",
      "clean": "yarn workspaces foreach -A run clean",
      "changeset": "changeset",
      "version-packages": "changeset version",
      "release": "yarn build && changeset publish"
    },
    "devDependencies": {
      "@changesets/cli": "^2.27.1",
      "@rollup/plugin-commonjs": "^25.0.7",
      "@rollup/plugin-node-resolve": "^15.2.3",
      "@rollup/plugin-typescript": "^11.1.5",
      "@types/node": "^20.10.5",
      "@typescript-eslint/eslint-plugin": "^6.15.0",
      "@typescript-eslint/parser": "^6.15.0",
      "eslint": "^8.56.0",
      "eslint-config-prettier": "^9.1.0",
      "eslint-plugin-prettier": "^5.1.2",
      "prettier": "^3.1.1",
      "rollup": "^4.9.1",
      "rollup-plugin-dts": "^6.1.0",
      "tslib": "^2.6.2",
      "typescript": "^5.3.3"
    },
    "engines": {
      "node": ">=18.0.0",
      "yarn": ">=1.22.0"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  No lockfile
